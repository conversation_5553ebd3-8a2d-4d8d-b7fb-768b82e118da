import React, { useEffect, useRef } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  MapPin,
  Map,
  Monitor,
  BookOpen,
  Tag,
  LogOut,
  Menu,
  X
} from 'lucide-react';

const sidebarItems = [
  { icon: LayoutDashboard, label: 'Dashboard', path: '/dashboard' },
  { icon: MapPin, label: 'Cities', path: '/cities' },
  { icon: Map, label: 'Zones', path: '/zones' },
  { icon: Monitor, label: 'Screens', path: '/screens' },
  { icon: BookOpen, label: 'Bookings', path: '/bookings' },
  { icon: Tag, label: 'Coupons', path: '/coupons' },
];

export default function Layout({ children }: { children: React.ReactNode }) {
  const [isSidebarOpen, setIsSidebarOpen] = React.useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const sidebarRef = useRef<HTMLDivElement>(null);

  const handleLogout = () => {
    navigate('/');
  };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        setIsSidebarOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setIsSidebarOpen(false);
  }, [location]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile menu button */}
      <button
        className="fixed top-4 left-4 z-50 lg:hidden text-gray-600 hover:text-gray-900"
        onClick={(e) => {
          e.stopPropagation();
          setIsSidebarOpen(!isSidebarOpen);
        }}
      >
        {isSidebarOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* Mini Sidebar (Desktop) */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:w-20 lg:flex-col">
        <div className="flex flex-1 flex-col min-h-0 bg-white border-r border-gray-200">
          <div className="flex-1">
            <div className="flex items-center justify-center h-16 bg-primary-800">
              <span className="text-xl font-bold text-white">3S</span>
            </div>
            <nav className="flex-1 space-y-1 px-2 py-4">
              {sidebarItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`group relative flex items-center p-2 justify-center rounded-lg ${
                    location.pathname === item.path
                      ? 'bg-primary-100 text-primary-800'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <item.icon className="h-6 w-6" />
                  <span className="absolute left-12 ml-6 w-auto min-w-max p-2 rounded-md shadow-md
                    text-white bg-primary-800 text-xs font-bold transition-all duration-100
                    scale-0 origin-left group-hover:scale-100">
                    {item.label}
                  </span>
                </Link>
              ))}
            </nav>
          </div>
          <div className="flex-shrink-0 flex pb-5 px-2">
            <button
              onClick={handleLogout}
              className="group relative flex items-center p-2 w-full justify-center text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              <LogOut className="h-6 w-6" />
              <span className="absolute left-12 ml-6 w-auto min-w-max p-2 rounded-md shadow-md
                text-white bg-primary-800 text-xs font-bold transition-all duration-100
                scale-0 origin-left group-hover:scale-100">
                Logout
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Full Sidebar (Mobile) */}
      <aside
        ref={sidebarRef}
        className={`fixed inset-y-0 left-0 z-40 w-64 transform bg-white shadow-lg transition-transform duration-200 ease-in-out lg:hidden ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="h-full flex flex-col">
          <div className="flex items-center justify-center h-16 bg-primary-800">
            <h1 className="text-xl font-bold text-white">3Shull Admin</h1>
          </div>

          <nav className="flex-1 px-4 py-6 space-y-2">
            {sidebarItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center px-4 py-3 text-sm rounded-lg ${
                  location.pathname === item.path
                    ? 'bg-primary-100 text-primary-800'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.label}
              </Link>
            ))}
          </nav>

          <div className="p-4 border-t">
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-4 py-3 text-sm text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <LogOut className="w-5 h-5 mr-3" />
              Logout
            </button>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <main className="lg:pl-20">
        <div className="p-6 ml-8 lg:ml-0">
          {children}
        </div>
      </main>
    </div>
  );
}