import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2, X, Power, Monitor, ChevronDown, ChevronRight } from 'lucide-react';
import type { Zone, City, Screen } from '../types';
import { supabase } from '../supabaseClient';
import { MOCK_CITIES, MOCK_ZONES, ALL_MOCK_SCREENS } from '../mockData';

interface ZoneFormData {
  name: string;
  cityId: string;
  subZone: string;
  pricePerYear: number | '';
  description: string;
  imageUrl: string;
  isAvailable: boolean;
}

interface ScreenFormData {
  name: string;
  screenNumber: string;
  address: string;
  location: string;
  size: string;
  isAvailable: boolean;
}

const defaultFormData: ZoneFormData = {
  name: '',
  cityId: '',
  subZone: '',
  pricePerYear: '',
  description: '',
  imageUrl: '',
  isAvailable: true
};

const defaultScreenFormData: ScreenFormData = {
  name: '',
  screenNumber: '',
  address: '',
  location: '',
  size: '42"',
  isAvailable: true,
};

export default function Zones() {
  const [zones, setZones] = useState<Zone[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [screens, setScreens] = useState<Screen[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isScreenModalOpen, setIsScreenModalOpen] = useState(false);
  const [editingZone, setEditingZone] = useState<Zone | null>(null);
  const [editingScreen, setEditingScreen] = useState<Screen | null>(null);
  const [selectedZoneForScreens, setSelectedZoneForScreens] = useState<Zone | null>(null);
  const [formData, setFormData] = useState<ZoneFormData>(defaultFormData);
  const [screenFormData, setScreenFormData] = useState<ScreenFormData>(defaultScreenFormData);
  const [previewImage, setPreviewImage] = useState<string>('');
  const [expandedZones, setExpandedZones] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Using mock data since database tables are not yet created
    setCities(MOCK_CITIES.filter(city => city.isavailable));
    setZones(MOCK_ZONES);
    setScreens(ALL_MOCK_SCREENS);
  }, []);

  const handleOpenModal = (zone?: Zone) => {
    if (zone) {
      const formDataFromZone: ZoneFormData = {
        name: zone.name || '',
        cityId: zone.city_id || '',
        subZone: zone.subZone || '',
        pricePerYear: zone.pricePerYear === null || zone.pricePerYear === undefined ? '' : zone.pricePerYear,
        description: zone.description || '',
        imageUrl: zone.imageUrl || '',
        isAvailable: zone.isAvailable ?? true,
      };
      setEditingZone(zone);
      setFormData(formDataFromZone);
      setPreviewImage(zone.imageUrl || '');
    } else {
      setEditingZone(null);
      setFormData(defaultFormData);
      setPreviewImage('');
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingZone(null);
    setFormData(defaultFormData);
    setPreviewImage('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const submissionData = {
      name: formData.name,
      city: formData.city,
      sub_zone: formData.subZone,
      price_year: typeof formData.pricePerYear === 'string' ? 0 : Number(formData.pricePerYear),
      description: formData.description,
      image_url: formData.imageUrl,
      isavailable: formData.isAvailable,
    };

    if (editingZone) {
      const { data, error } = await supabase
        .from('zones')
        .update([submissionData])
        .eq('id', editingZone.id)
        // Select with aliases to get camelCase data back, matching the Zone type
        .select(`
          id,
          name,
          city,
          subZone: sub_zone,
          pricePerYear: price_year,
          description,
          imageUrl: image_url,
          isAvailable: isavailable,
          created_at,
          updated_at
        `);

      if (error) {
        console.error('Error updating zone:', error);
      } else if (data) { // Ensure data is not null before using
        // Now data[0] has camelCase keys, matching the Zone type
        setZones(prevZones => prevZones.map(zone => zone.id === editingZone.id ? data[0] : zone));
      }
    } else {
      const { data, error } = await supabase
        .from('zones')
        .insert([submissionData])
        // Select with aliases for consistency
        .select(`
          id,
          name,
          city,
          subZone: sub_zone,
          pricePerYear: price_year,
          description,
          imageUrl: image_url,
          isAvailable: isavailable,
          created_at,
          updated_at
        `);

      if (error) {
        console.error('Error adding zone:', error);
      } else if (data) { // Ensure data is not null before using
        setZones(prevZones => [...prevZones, data[0]]);
      }
    }
    handleCloseModal();
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this zone?')) {
      const { error } = await supabase
        .from('zones')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting zone:', error);
      } else {
        setZones(prevZones => prevZones.filter(zone => zone.id !== id));
      }
    }
  };

  const toggleAvailability = async (id: string) => {
    const zoneToUpdate = zones.find(zone => zone.id === id);
    if (!zoneToUpdate) return;

    // Toggle the 'isavailable' field
    const newAvailability = !zoneToUpdate.isAvailable;
    const { error } = await supabase
      .from('zones')
      .update({ isavailable: newAvailability }) // Update the correct DB column
      .eq('id', id);

    if (error) {
      console.error('Error toggling availability:', error);
    } else {
      // Update local state
      setZones(prevZones => prevZones.map(zone =>
        zone.id === id ? {
          ...zone,
          isAvailable: newAvailability // Update the correct state property
        } : zone
      ));
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Manage Zones</h1>
        <button
          onClick={() => handleOpenModal()}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add New Zone
        </button>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {zones.map((zone) => (
          <div key={zone.id} className="bg-white rounded-lg shadow overflow-hidden">
            <div className="h-48 w-full overflow-hidden">
              <img
                src={zone.imageUrl}
                alt={zone.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium text-gray-900">{zone.name}</h3>
                <button
                  onClick={() => toggleAvailability(zone.id)}
                  className={`${
                    zone.isAvailable
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  } px-2 py-1 rounded-full text-xs font-medium inline-flex items-center`}
                >
                  <Power className="h-3 w-3 mr-1" />
                  {zone.isAvailable ? 'Active' : 'Inactive'}
                </button>
              </div>
              <p className="mt-2 text-sm text-gray-500">{zone.description}</p>
              <div className="mt-4 space-y-2">
                <div className="text-sm text-gray-500">
                  City: {zone.city}
                </div>
                <div className="text-sm text-gray-500">
                  Sub Zone: {zone.subZone}
                </div>
                <div className="text-sm font-medium text-gray-900">
                  ₹{(zone.pricePerYear || 0).toLocaleString('en-IN')}/year
                </div>
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => handleOpenModal(zone)}
                  className="text-primary-600 hover:text-primary-900"
                >
                  <Pencil className="h-5 w-5" />
                </button>
                <button
                  onClick={() => handleDelete(zone.id)}
                  className="text-red-600 hover:text-red-900"
                >
                  <Trash2 className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            <div className="relative bg-white rounded-lg max-w-2xl w-full">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  onClick={handleCloseModal}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingZone ? 'Edit Zone' : 'Add New Zone'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Zone Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                        City
                      </label>
                      <select
                        id="city"
                        value={formData.city}
                        onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      >
                        <option value="">Select a city</option>
                        {cities.map((city) => (
                          <option key={city} value={city}>
                            {city}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subZone" className="block text-sm font-medium text-gray-700">
                      Sub Zone
                    </label>
                    <input
                      type="text"
                      id="subZone"
                      value={formData.subZone}
                      onChange={(e) => setFormData({ ...formData, subZone: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                      placeholder="e.g., Near Forum Mall, Metro Station Exit B"
                    />
                  </div>

                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                      Description
                    </label>
                    <textarea
                      id="description"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700">
                      Image URL
                    </label>
                    <div className="mt-1 flex rounded-md shadow-sm">
                      <input
                        type="url"
                        id="imageUrl"
                        value={formData.imageUrl}
                        onChange={(e) => {
                          setFormData({ ...formData, imageUrl: e.target.value });
                          setPreviewImage(e.target.value);
                        }}
                        className="flex-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      />
                    </div>
                    {previewImage && (
                      <div className="mt-2">
                        <img
                          src={previewImage}
                          alt="Preview"
                          className="h-32 w-full object-cover rounded-md"
                          onError={() => setPreviewImage('')}
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <label htmlFor="pricePerYear" className="block text-sm font-medium text-gray-700">
                      Price per Year
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">₹</span>
                      </div>
                      <input
                        type="number"
                        id="pricePerYear"
                        value={formData.pricePerYear}
                        onChange={(e) => setFormData({ ...formData, pricePerYear: e.target.value === '' ? '' : Number(e.target.value) })}
                        className="pl-7 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                         required
                         min="0"
                         // Removed step="1000" to allow any value
                       />
                     </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Availability
                    </label>
                    <div className="mt-2">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.isAvailable}
                          onChange={(e) => setFormData({ ...formData, isAvailable: e.target.checked })}
                          className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          Zone is available for booking
                        </span>
                      </label>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={handleCloseModal}
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      {editingZone ? 'Save Changes' : 'Add Zone'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
