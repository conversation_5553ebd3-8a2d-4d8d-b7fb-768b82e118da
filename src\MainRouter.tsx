import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import { BusinessInfo, DateRange, Zone, CartItem, Booking, PackageType, Screen, City } from './types';
import { Stepper } from './components/Stepper';
import { BusinessInfoForm } from './components/BusinessInfoForm';
import { PackageSelection } from './components/PackageSelection';
import { DateSelection } from './components/DateSelection';
import { VideoUpload } from './components/VideoUpload/VideoUpload';
import { ZoneList } from './components/ZoneList';
import { CityList } from './components/CityList';
import { ScreenList } from './components/ScreenList';
import { CartSummary } from './components/CartSummary';
import { PaymentStatus } from './components/PaymentStatus';
import { BookingConfirmation } from './components/BookingConfirmation';
import { Dashboard } from './components/Dashboard/Dashboard';
import { LoginForm } from './components/Auth/LoginForm';
import { useAuth } from './contexts/AuthContext';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from './lib/supabase';
import { getUserBookings, updateBookingVideo, createBooking, updatePaymentStatus } from './lib/bookings';
import { MOCK_ZONES, MOCK_SCREENS } from './mockData';

type Step = 'business-info' | 'package-selection' | 'date-selection' | 'video-upload' | 'city-selection' | 'zone-selection' | 'screen-selection' | 'cart' | 'payment' | 'confirmation';

const STEP_TITLES = {
  'business-info': 'Tell Us About Your Business',
  'package-selection': 'Choose Your Package',
  'date-selection': 'Choose Your Booking Dates',
  'video-upload': 'Upload Your Video',
  'city-selection': 'Select Cities',
  'zone-selection': 'Available Zones',
  'screen-selection': 'Select Screens',
  'cart': 'Your Booking Summary',
  'payment': 'Complete Your Payment',
  'confirmation': 'Thank You for Booking',
};

function PrivateRoute({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();

  if (user === null) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
}

function BookingForm() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState<Step>('business-info');
  const [businessInfo, setBusinessInfo] = useState<BusinessInfo>({
    businessName: '',
    phone: '',
    email: '',
    listingType: '',
    description: '',
    packageType: '',
  });
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: null,
    endDate: null,
  });
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [selectedCities, setSelectedCities] = useState<City[]>([]);
  const [selectedScreens, setSelectedScreens] = useState<Screen[]>([]);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [bookingId, setBookingId] = useState<string>('');
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<number>(Date.now());

  const isPaymentStarted = currentStep === 'payment' || currentStep === 'confirmation';

  // Generate steps based on package type
  const getStepsForPackage = (packageType: string) => {
    const baseSteps = [
      {
        name: 'Business Info',
        status: currentStep === 'business-info' ? 'current' as const :
                ['package-selection', 'date-selection', 'video-upload', 'city-selection', 'zone-selection', 'screen-selection', 'cart', 'payment', 'confirmation'].includes(currentStep) ? 'complete' as const : 'upcoming' as const,
        clickable: !isPaymentStarted
      },
      {
        name: 'Package',
        status: currentStep === 'package-selection' ? 'current' as const :
                ['date-selection', 'video-upload', 'city-selection', 'zone-selection', 'screen-selection', 'cart', 'payment', 'confirmation'].includes(currentStep) ? 'complete' as const : 'upcoming' as const,
        clickable: !isPaymentStarted
      },
      {
        name: 'Dates',
        status: currentStep === 'date-selection' ? 'current' as const :
                ['video-upload', 'city-selection', 'zone-selection', 'screen-selection', 'cart', 'payment', 'confirmation'].includes(currentStep) ? 'complete' as const : 'upcoming' as const,
        clickable: !isPaymentStarted
      },
      {
        name: 'Video',
        status: currentStep === 'video-upload' ? 'current' as const :
                ['city-selection', 'zone-selection', 'screen-selection', 'cart', 'payment', 'confirmation'].includes(currentStep) ? 'complete' as const : 'upcoming' as const,
        clickable: !isPaymentStarted
      }
    ];

    // For per-screen package: show "Selection" step that covers city and screen selection
    // For fixed-price package: show "Selection" step that covers city, zone, and screen selection
    baseSteps.push({
      name: 'Selection',
      status: ['city-selection', 'zone-selection', 'screen-selection'].includes(currentStep) ? 'current' as const :
              ['cart', 'payment', 'confirmation'].includes(currentStep) ? 'complete' as const : 'upcoming' as const,
      clickable: !isPaymentStarted
    });

    // Add payment step
    baseSteps.push({
      name: 'Payment',
      status: currentStep === 'cart' || currentStep === 'payment' ? 'current' as const :
              currentStep === 'confirmation' ? 'complete' as const : 'upcoming' as const,
      clickable: false
    });

    return baseSteps;
  };

  const steps = getStepsForPackage(businessInfo.packageType || '');

  const handleBusinessInfoSubmit = (data: BusinessInfo) => {
    setBusinessInfo(data);
    setCurrentStep('package-selection');
  };

  const handlePackageSelect = (packageType: PackageType) => {
    setBusinessInfo({ ...businessInfo, packageType });
  };

  const handlePackageSubmit = () => {
    setCurrentStep('date-selection');
  };

  const handleStepClick = (stepIndex: number) => {
    const getStepOrder = (packageType: string): Step[] => {
      if (packageType === 'per-screen') {
        // Per-screen package flow: excludes zone-selection
        return ['business-info', 'package-selection', 'date-selection', 'video-upload', 'city-selection', 'screen-selection', 'cart', 'payment', 'confirmation'];
      } else {
        // Fixed-price package flow: includes zone-selection
        return ['business-info', 'package-selection', 'date-selection', 'video-upload', 'city-selection', 'zone-selection', 'screen-selection', 'cart', 'payment', 'confirmation'];
      }
    };

    const stepOrder = getStepOrder(businessInfo.packageType || '');
    if (!isPaymentStarted && stepIndex < stepOrder.length) {
      setCurrentStep(stepOrder[stepIndex]);
    }
  };

  const handleBack = () => {
    const packageType = businessInfo.packageType;
    console.log('Back button clicked - Current step:', currentStep, 'Package type:', packageType);

    // Handle different flows based on package type
    if (currentStep === 'cart') {
      if (packageType === 'fixed-price') {
        console.log('Going back from cart to zone-selection (fixed-price)');
        setCurrentStep('zone-selection');
      } else {
        console.log('Going back from cart to screen-selection (per-screen)');
        setCurrentStep('screen-selection');
      }
      return;
    }

    // Handle screen-selection back navigation based on package type
    if (currentStep === 'screen-selection') {
      if (packageType === 'per-screen') {
        // For per-screen package, go directly back to city selection
        console.log('Going back from screen-selection to city-selection (per-screen)');
        setCurrentStep('city-selection');
      } else {
        // For fixed-price package, go back to zone selection
        console.log('Going back from screen-selection to zone-selection (fixed-price)');
        setCurrentStep('zone-selection');
      }
      return;
    }

    // For other steps, use the appropriate flow based on package type
    const getStepOrder = (packageType: string): Step[] => {
      if (packageType === 'per-screen') {
        // Per-screen package flow: excludes zone-selection
        return ['business-info', 'package-selection', 'date-selection', 'video-upload', 'city-selection', 'screen-selection', 'cart', 'payment', 'confirmation'];
      } else {
        // Fixed-price package flow: includes zone-selection
        return ['business-info', 'package-selection', 'date-selection', 'video-upload', 'city-selection', 'zone-selection', 'screen-selection', 'cart', 'payment', 'confirmation'];
      }
    };

    const stepOrder = getStepOrder(packageType);
    const currentIndex = stepOrder.indexOf(currentStep);
    console.log('Step order:', stepOrder, 'Current index:', currentIndex);
    if (currentIndex > 0) {
      const previousStep = stepOrder[currentIndex - 1];
      console.log('Going back to:', previousStep);
      setCurrentStep(previousStep);
    }
  };

  const handleDateSubmit = () => {
    if (dateRange.startDate && dateRange.endDate) {
      // Update all existing cart items with the new date range
      const updatedCartItems = cartItems.map(item => ({
        ...item,
        dateRange: dateRange
      }));
      setCartItems(updatedCartItems);

      setCurrentStep('video-upload');
    } else {
      alert('Please select both start and end dates');
    }
  };

  const handleVideoUpload = async (file: File) => {
    setVideoFile(file);
    try {
      const videoFileName = `${uuidv4()}-${file.name}`;
      const { data, error } = await supabase.storage
        .from('videos')
        .upload(videoFileName, file);

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from('videos')
        .getPublicUrl(data.path);
      setVideoUrl(publicUrl);

      // Don't automatically proceed to next step
      // Let the user preview and confirm
    } catch (error) {
      console.error('Error uploading video:', error);
    }
  };

  const handleCitySelect = (city: City) => {
    // Only allow one city selection for both packages
    setSelectedCities([city]);
  };

  const handleCitySelectionNext = () => {
    const packageType = businessInfo.packageType;
    console.log('City selection next - Package type:', packageType);
    if (packageType === 'fixed-price') {
      console.log('Navigating to zone-selection for fixed-price package');
      setCurrentStep('zone-selection');
    } else {
      console.log('Navigating to screen-selection for per-screen package');
      setCurrentStep('screen-selection');
    }
  };

  const handleScreensSelected = (screens: Screen[]) => {
    setSelectedScreens(screens);
  };

  const handleScreenSelectionNext = () => {
    // For per-screen package, add selected screens to cart
    // We need to create cart items from selected screens
    const screensByZone = selectedScreens.reduce((acc, screen) => {
      if (!acc[screen.zone_id]) {
        acc[screen.zone_id] = [];
      }
      acc[screen.zone_id].push(screen);
      return acc;
    }, {} as Record<string, Screen[]>);

    // Create cart items for each zone
    const newCartItems: CartItem[] = Object.entries(screensByZone).map(([zoneId, screens]) => {
      // Find the zone data (we'll need to get this from MOCK_ZONES)
      const zone = MOCK_ZONES.find(z => z.id === zoneId);
      if (!zone) throw new Error(`Zone ${zoneId} not found`);

      return {
        zone,
        dateRange,
        videoUrl: videoFile?.name,
        selectedScreens: screens
      };
    });

    setCartItems(newCartItems);
    setCurrentStep('cart');
  };

  const handleRemoveFromCart = (zoneId: string) => {
    setCartItems(cartItems.filter(item => item.zone.id !== zoneId));
  };

  const handleAddToCart = (zone: Zone, selectedScreens: Screen[]) => {
    // Check if this zone is already in the cart
    const existingItemIndex = cartItems.findIndex(item => item.zone.id === zone.id);

    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedCartItems = [...cartItems];
      updatedCartItems[existingItemIndex] = {
        ...updatedCartItems[existingItemIndex],
        selectedScreens
      };
      setCartItems(updatedCartItems);
    } else {
      // Add new item
      setCartItems([...cartItems, {
        zone,
        dateRange,
        videoUrl: videoFile?.name,
        selectedScreens
      }]);
    }

    // If no screens selected, remove the item from cart
    if (selectedScreens.length === 0) {
      setCartItems(cartItems.filter(item => item.zone.id !== zone.id));
    }
  };

  const handleProceedToPayment = async () => {
    try {
      console.log('Starting payment process...');
      setPaymentStatus('processing');

      console.log('Creating booking with data:', {
        user_id: user!.id,
        zones: cartItems.map(item => ({ id: item.zone.id })),
        start_date: cartItems[0].dateRange.startDate!.toISOString(),
        end_date: cartItems[0].dateRange.endDate!.toISOString(),
        business_name: businessInfo.businessName,
        phone_number: businessInfo.phone,
        email: businessInfo.email,
        listing_type: businessInfo.listingType,
        description: businessInfo.description,
        video_url: videoUrl || null
      });

      const bookingId = await createBooking({
        user_id: user!.id,
        zones: cartItems.map(item => ({ id: item.zone.id })),
        start_date: cartItems[0].dateRange.startDate!.toISOString(),
        end_date: cartItems[0].dateRange.endDate!.toISOString(),
        business_name: businessInfo.businessName,
        phone_number: businessInfo.phone,
        email: businessInfo.email,
        listing_type: businessInfo.listingType,
        description: businessInfo.description,
        video_url: videoUrl || null
      });

      console.log('Booking created successfully with ID:', bookingId);

      if (bookingId && videoUrl) {
        console.log('Updating booking with video URL...');
        await updateBookingVideo(bookingId, videoUrl);
        console.log('Video URL updated successfully');
      }

      return bookingId;
    } catch (error) {
      console.error('Booking creation failed:', error);
      setPaymentStatus('error');
      throw error;
    }
  };

  const handlePaymentComplete = (bookingId?: string) => {
    console.log('handlePaymentComplete called with bookingId:', bookingId);
    try {
      if (bookingId) {
        console.log('Setting bookingId state to:', bookingId);
        setBookingId(bookingId);
      }
      console.log('Setting paymentStatus to success');
      setPaymentStatus('success');
      console.log('Setting currentStep to confirmation');
      setCurrentStep('confirmation');

      // Force a re-render by updating a timestamp
      setLastUpdated(new Date().getTime());

      console.log('Navigation to confirmation page should be complete');
    } catch (error) {
      console.error('Error in handlePaymentComplete:', error);
    }
  };

  // Use lastUpdated to force re-render when needed
  useEffect(() => {
    console.log('Component re-rendered due to lastUpdated change:', lastUpdated);
  }, [lastUpdated]);

  return (
    <div className="min-h-screen bg-[#23044b]">
      <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl">
          <Stepper
            steps={steps}
            onStepClick={handleStepClick}
          />
          <div className="mt-8 bg-white rounded-lg shadow-xl p-6">
            {/* Add Back button for all steps except first step and payment/confirmation steps */}
            {currentStep !== 'business-info' && !isPaymentStarted && (
              <button
                onClick={handleBack}
                className="mb-4 flex items-center text-sm text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </button>
            )}

            {/* Render current step components */}
            {currentStep === 'business-info' && (
              <BusinessInfoForm
                businessInfo={businessInfo}
                onChange={(data) => setBusinessInfo({ ...businessInfo, ...data })}
                onSubmit={handleBusinessInfoSubmit}
              />
            )}
            {currentStep === 'package-selection' && (
              <PackageSelection
                selectedPackage={businessInfo.packageType || ''}
                businessInfo={businessInfo}
                onPackageSelect={handlePackageSelect}
                onSubmit={handlePackageSubmit}
              />
            )}
            {currentStep === 'date-selection' && (
              <DateSelection
                dateRange={dateRange}
                onDateChange={setDateRange}
                onSubmit={handleDateSubmit}
              />
            )}
            {currentStep === 'video-upload' && (
              <VideoUpload
                onVideoUpload={handleVideoUpload}
                currentVideoUrl={videoUrl}
                onContinue={() => setCurrentStep('city-selection')}
              />
            )}

            {currentStep === 'city-selection' && (
              <CityList
                dateRange={dateRange}
                cartItems={cartItems}
                onCitySelect={handleCitySelect}
                onNext={handleCitySelectionNext}
                businessInfo={businessInfo}
              />
            )}

            {currentStep === 'zone-selection' && (
              <ZoneList
                dateRange={dateRange}
                cartItems={cartItems}
                onAddToCart={handleAddToCart}
                onRemoveFromCart={handleRemoveFromCart}
                onNext={() => setCurrentStep('cart')}
                businessInfo={businessInfo}
                selectedCities={selectedCities}
              />
            )}

            {currentStep === 'screen-selection' && (
              <ScreenList
                selectedCities={selectedCities}
                dateRange={dateRange}
                cartItems={cartItems}
                onScreensSelected={handleScreensSelected}
                onNext={handleScreenSelectionNext}
                businessInfo={businessInfo}
              />
            )}

            {currentStep === 'cart' && (
              <CartSummary
                cartItems={cartItems}
                onRemoveItem={handleRemoveFromCart}
                onProceedToPayment={handleProceedToPayment}
                onPaymentComplete={handlePaymentComplete}
                businessInfo={businessInfo}
              />
            )}

            {currentStep === 'payment' && (
              <PaymentStatus
                status={paymentStatus}
                onRetry={() => setPaymentStatus('processing')}
              />
            )}

            {currentStep === 'confirmation' && (
              <BookingConfirmation
                bookingId={bookingId}
                cartItems={cartItems}
                email={businessInfo.email}
                onBookAnother={() => {
                  setCurrentStep('business-info');
                  setBusinessInfo({
                    businessName: '',
                    phone: '',
                    email: '',
                    listingType: '',
                    description: '',
                    packageType: ''
                  });
                  setCartItems([]);
                  setPaymentStatus('idle');
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function MainRouter() {
  const { user, logout } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);

  useEffect(() => {
    if (user?.id) {
      const loadBookings = async () => {
        try {
          const userBookings = await getUserBookings(user.id);
          setBookings(userBookings);
          console.log('Loaded bookings:', userBookings); // Add this for debugging
        } catch (error) {
          console.error('Failed to load bookings:', error);
        }
      };
      loadBookings();
    }
  }, [user]);

  return (
    <Router>
      <Routes>
        <Route path="/login" element={<LoginForm />} />
        <Route path="/dashboard" element={
          <PrivateRoute>
            <Dashboard
              user={user!}
              bookings={bookings}
              onLogout={logout}
              onVideoUpload={async (bookingId, file) => {
                try {
                  const videoFileName = `${uuidv4()}-${file.name}`;
                  const { data, error } = await supabase.storage
                    .from('videos')
                    .upload(videoFileName, file);

                  if (error) throw error;

                  const { data: { publicUrl } } = supabase.storage
                    .from('videos')
                    .getPublicUrl(data.path);

                  // Update the booking with the video URL
                  await updateBookingVideo(bookingId, publicUrl);

                  // Refresh bookings
                  const updatedBookings = await getUserBookings(user!.id);
                  setBookings(updatedBookings);
                } catch (error) {
                  console.error('Video upload failed:', error);
                }
              }}
            />
          </PrivateRoute>
        } />
        <Route path="/book" element={
          <PrivateRoute>
            <BookingForm />
          </PrivateRoute>
        } />
        <Route path="/" element={<Navigate to="/login" replace />} />
      </Routes>
    </Router>
  );
}
