import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2, X, Power, Monitor } from 'lucide-react';
import type { Screen, Zone } from '../types';
import { supabase } from '../supabaseClient';
import { MOCK_ZONES, ALL_MOCK_SCREENS } from '../mockData';

interface ScreenFormData {
  name: string;
  screenNumber: string;
  zoneId: string;
  address: string;
  location: string;
  size: string;
  isAvailable: boolean;
}

const defaultFormData: ScreenFormData = {
  name: '',
  screenNumber: '',
  zoneId: '',
  address: '',
  location: '',
  size: '42"',
  isAvailable: true
};

const screenSizes = ['32"', '42"', '55"', '65"', '75"'];

export default function Screens() {
  const [screens, setScreens] = useState<Screen[]>([]);
  const [zones, setZones] = useState<Zone[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingScreen, setEditingScreen] = useState<Screen | null>(null);
  const [formData, setFormData] = useState<ScreenFormData>(defaultFormData);
  const [selectedZone, setSelectedZone] = useState<string>('');

  useEffect(() => {
    // Using mock data since database tables are not yet created
    setZones(MOCK_ZONES.filter(zone => zone.isAvailable));
    setScreens(ALL_MOCK_SCREENS);
  }, []);

  const filteredScreens = selectedZone
    ? screens.filter(screen => screen.zone_id === selectedZone)
    : screens;

  const handleOpenModal = (screen?: Screen) => {
    if (screen) {
      setEditingScreen(screen);
      setFormData({
        name: screen.name || '',
        screenNumber: screen.screen_number || '',
        zoneId: screen.zone_id || '',
        address: screen.address || '',
        location: screen.location || '',
        size: screen.size || '42"',
        isAvailable: screen.isavailable ?? true,
      });
    } else {
      setEditingScreen(null);
      setFormData(defaultFormData);
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingScreen(null);
    setFormData(defaultFormData);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const submissionData = {
      name: formData.name,
      screen_number: formData.screenNumber,
      zone_id: formData.zoneId,
      address: formData.address,
      location: formData.location,
      size: formData.size,
      isavailable: formData.isAvailable,
    };

    if (editingScreen) {
      const { data, error } = await supabase
        .from('screens')
        .update(submissionData)
        .eq('id', editingScreen.id)
        .select(`
          id,
          name,
          screen_number,
          zone_id,
          address,
          location,
          size,
          isavailable,
          created_at,
          updated_at,
          zones:zone_id (name)
        `)
        .single();

      if (error) {
        console.error('Error updating screen:', error);
      } else if (data) {
        setScreens(prevScreens => prevScreens.map(screen => screen.id === editingScreen.id ? data : screen));
      }
    } else {
      const { data, error } = await supabase
        .from('screens')
        .insert([submissionData])
        .select(`
          id,
          name,
          screen_number,
          zone_id,
          address,
          location,
          size,
          isavailable,
          created_at,
          updated_at,
          zones:zone_id (name)
        `)
        .single();

      if (error) {
        console.error('Error adding screen:', error);
      } else if (data) {
        setScreens(prevScreens => [data, ...prevScreens]);
      }
    }
    handleCloseModal();
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this screen?')) {
      const { error } = await supabase
        .from('screens')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting screen:', error);
      } else {
        setScreens(prevScreens => prevScreens.filter(screen => screen.id !== id));
      }
    }
  };

  const toggleAvailability = async (id: string) => {
    const screenToUpdate = screens.find(screen => screen.id === id);
    if (!screenToUpdate) return;

    const newAvailability = !screenToUpdate.isavailable;
    const { error } = await supabase
      .from('screens')
      .update({ isavailable: newAvailability })
      .eq('id', id);

    if (error) {
      console.error('Error toggling availability:', error);
    } else {
      setScreens(prevScreens => prevScreens.map(screen =>
        screen.id === id ? { ...screen, isavailable: newAvailability } : screen
      ));
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Manage Screens</h1>
        <button
          onClick={() => handleOpenModal()}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add New Screen
        </button>
      </div>

      {/* Zone Filter */}
      <div className="flex items-center space-x-4">
        <label htmlFor="zoneFilter" className="text-sm font-medium text-gray-700">
          Filter by Zone:
        </label>
        <select
          id="zoneFilter"
          value={selectedZone}
          onChange={(e) => setSelectedZone(e.target.value)}
          className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-primary-500 focus:ring-primary-500"
        >
          <option value="">All Zones</option>
          {zones.map((zone) => (
            <option key={zone.id} value={zone.id}>
              {zone.name} - {zone.city_name}
            </option>
          ))}
        </select>
      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {filteredScreens.map((screen) => (
          <div key={screen.id} className="bg-white rounded-lg shadow border overflow-hidden">
            <div className="p-4">
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center">
                  <Monitor className="h-5 w-5 text-gray-500 mr-2" />
                  <h3 className="text-sm font-medium text-gray-900">{screen.name}</h3>
                </div>
                <button
                  onClick={() => toggleAvailability(screen.id)}
                  className={`${
                    screen.isavailable
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  } px-2 py-1 rounded-full text-xs font-medium inline-flex items-center`}
                >
                  <Power className="h-3 w-3 mr-1" />
                  {screen.isavailable ? 'Active' : 'Inactive'}
                </button>
              </div>

              <div className="space-y-1 text-xs text-gray-600 mb-4">
                <p><span className="font-medium">Screen #:</span> {screen.screen_number}</p>
                <p><span className="font-medium">Zone:</span> {screen.zone_name}</p>
                <p><span className="font-medium">Location:</span> {screen.location}</p>
                <p><span className="font-medium">Address:</span> {screen.address}</p>
                <p><span className="font-medium">Size:</span> {screen.size}</p>
              </div>

              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => handleOpenModal(screen)}
                  className="text-primary-600 hover:text-primary-900"
                >
                  <Pencil className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(screen.id)}
                  className="text-red-600 hover:text-red-900"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            <div className="relative bg-white rounded-lg max-w-2xl w-full">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  onClick={handleCloseModal}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingScreen ? 'Edit Screen' : 'Add New Screen'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Screen Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="screenNumber" className="block text-sm font-medium text-gray-700">
                        Screen Number
                      </label>
                      <input
                        type="text"
                        id="screenNumber"
                        value={formData.screenNumber}
                        onChange={(e) => setFormData({ ...formData, screenNumber: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="zoneId" className="block text-sm font-medium text-gray-700">
                      Zone
                    </label>
                    <select
                      id="zoneId"
                      value={formData.zoneId}
                      onChange={(e) => setFormData({ ...formData, zoneId: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                    >
                      <option value="">Select a zone</option>
                      {zones.map((zone) => (
                        <option key={zone.id} value={zone.id}>
                          {zone.name} - {zone.city_name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                      Address
                    </label>
                    <input
                      type="text"
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                      Location
                    </label>
                    <input
                      type="text"
                      id="location"
                      value={formData.location}
                      onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="size" className="block text-sm font-medium text-gray-700">
                      Screen Size
                    </label>
                    <select
                      id="size"
                      value={formData.size}
                      onChange={(e) => setFormData({ ...formData, size: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                    >
                      {screenSizes.map((size) => (
                        <option key={size} value={size}>
                          {size}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Availability
                    </label>
                    <div className="mt-2">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.isAvailable}
                          onChange={(e) => setFormData({ ...formData, isAvailable: e.target.checked })}
                          className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          Screen is available for booking
                        </span>
                      </label>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={handleCloseModal}
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      {editingScreen ? 'Save Changes' : 'Add Screen'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
