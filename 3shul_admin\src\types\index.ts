export interface User {
  id: string;
  email: string;
  name: string;
}

export interface City {
  id: string;
  name: string;
  description: string;
  image_url: string;
  isavailable: boolean;
  created_at: string;
  updated_at: string;
}

export interface Screen {
  id: string;
  name: string;
  screen_number: string;
  zone_id: string;
  zone_name?: string;
  address: string;
  location: string;
  size: string;
  isavailable: boolean;
  created_at: string;
  updated_at: string;
}

export interface Zone {
  id: string;
  name: string;
  city_id: string;
  city_name?: string;
  subZone: string;
  pricePerYear: number;
  description: string;
  imageUrl: string;
  isAvailable: boolean;
  screens?: Screen[];
}

export interface BookingZone {
  zone_id: string;
  zones: Zone;
}

export interface Booking {
  id: string;
  user_id: string;
  zone_id: string;
  start_date: string;
  end_date: string;
  listing_type: string;
  business_name: string;
  description: string;
  phone_number: string;
  email?: string; // Added email field
  video_url?: string;
  status: 'Pending' | 'Approved' | 'Fulfilled' | 'Cancelled';
  payment_id: string;
  created_at: string;
  updated_at: string;
  zones?: Zone;
  booking_zones?: BookingZone[];
  transactions?: Transaction[];
  users?: User;
}

export interface Transaction {
  id: string;
  razorpay_payment_id: string;
  razorpay_order_id?: string;
  amount: number;
  currency?: string;
  method?: string;
  status?: string;
  email?: string;
  contact?: string;
  fee?: number;
  tax?: number;
  created_at: string;
}

export interface EmailLog {
  id: string;
  recipient: string;
  subject: string;
  booking_id?: string;
  status: 'success' | 'failed';
  error_message?: string;
  sent_at: string;
  created_at: string;
}
